import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  Image,
  FlatList,
  TouchableOpacity,
  ScrollView,
  I18nManager,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { BottomNavigation } from "react-native-paper";
import Colors from "../components/Colors";
import { ListUsers, ListUserProfile } from "../components/UserLists";

// Force RTL for entire app
I18nManager.forceRTL(true);
I18nManager.allowRTL(true);

const HomeScreen = () => {
  const [activeTab, setActiveTab] = useState("direct");
  
  const routes = [
    { key: "direct", title: "الرسائل المباشرة", icon: "message-text" },
    { key: "channels", title: "القنوات", icon: "bullhorn" },
    { key: "archived", title: "المفضلة", icon: "star" },
  ];

  const renderScene = ({ route }) => {
    return (
      <View style={styles.sceneContainer}>
        {/* Only show profiles in direct messages tab */}
        {route.key === "direct" && renderTopProfiles()}
        
        <FlatList
          data={ListUsers()}
          keyExtractor={(item) => item.id.toString()}
          renderItem={renderMessageItem}
          contentContainerStyle={styles.messagesListContent}
        />
      </View>
    );
  };

  const renderTopProfiles = () => {
    const data = ListUserProfile();
    return (
      <ScrollView
        horizontal={true}
        showsHorizontalScrollIndicator={false}
        style={styles.topProfileContainer}
        contentContainerStyle={styles.topProfileContent}
        directionalLockEnabled={true}
      >
        {/* Add Profile Button First (Right side in RTL) */}
        <TouchableOpacity style={styles.addProfile}>
          <Ionicons name="add" size={24} color="#fff" />
        </TouchableOpacity>

        {/* User Profiles */}
        {data.map((user, index) => (
          <View key={index} style={styles.profileItem}>
            <Image source={user.avatar} style={styles.profileImage} />
            <Text style={styles.profileName}>{user.name}</Text>
          </View>
        ))}
      </ScrollView>
    );
  };

  const renderMessageItem = ({ item }) => (
    <View style={styles.messageItem}>
      <View style={styles.messageMeta}>
        <View style={styles.timeBadgeContainer}>
          <Text style={styles.messageTime}>{item.time}</Text>
          {item.unread > 0 && (
            <View style={styles.badge}>
              <Text style={styles.badgeText}>{item.unread}</Text>
            </View>
          )}
        </View>
      </View>
      <View style={styles.messageTextContainer}>
        <Text style={styles.messageName}>{item.name}</Text>
        <Text style={styles.messageText}>{item.lastMessage}</Text>
      </View>
      <View style={styles.avatarCircle}>
        <Image source={item.avatar} style={styles.avatarImage} />
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerText}>الفريق الرئيسي</Text>
        <MaterialCommunityIcons name="inbox-multiple-outline" size={24} color="black" />
      </View>

      {/* Bottom Navigation */}
      <BottomNavigation
        navigationState={{ 
          index: routes.findIndex(route => route.key === activeTab), 
          routes 
        }}
        onIndexChange={(index) => setActiveTab(routes[index].key)}
        renderScene={renderScene}
        barStyle={styles.bottomBar}
        activeColor={Colors.primary}
        inactiveColor="#777"
        renderIcon={({ route, focused, color }) => (
          <MaterialCommunityIcons 
            name={route.icon} 
            size={24} 
            color={color} 
          />
        )}
        labeled={true}
        sceneAnimationEnabled={true}
      />
    </View>
  );
};

export default HomeScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  header: {
    flexDirection: "row",
    justifyContent: "flex-end",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingTop: 50,
    paddingBottom: 10,
    backgroundColor: "#fff",
  },
  headerText: {
    fontSize: 18,
    fontFamily: "Tajawal_700Bold",
    marginLeft: 10,
  },
  sceneContainer: {
    flex: 1,
  },
  bottomBar: {
    backgroundColor: "#fff",
    borderTopWidth: 1,
    borderTopColor: "#eee",
    paddingVertical: 4,
  },
  topProfileContainer: {
    marginVertical: 5,
    paddingHorizontal: 5,
  },
  topProfileContent: {
    flexDirection: "row-reverse",
    paddingHorizontal: 5,
  },
  profileItem: {
    alignItems: "center",
    marginHorizontal: 8,
    marginBottom: 30,
    width: 65,
  },
  profileImage: {
    width: 65,
    height: 65,
    borderRadius: 32.5,
    marginBottom: 4,
  },
  profileName: {
    fontSize: 12,
    fontFamily: "Tajawal_500Medium",
    textAlign: "center",
    writingDirection: "rtl",
  },
  addProfile: {
    width: 65,
    height: 65,
    borderRadius: 32.5,
    backgroundColor: "#0B735F",
    justifyContent: "center",
    alignItems: "center",
    marginHorizontal: 8,
  },
  messagesListContent: {
    paddingBottom: 1,
  },
  messageItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderColor: "#f0f0f0",
  },
  avatarCircle: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: "center",
    alignItems: "center",
    marginLeft: 10,
  },
  avatarImage: {
    width: 44,
    height: 44,
    borderRadius: 22,
  },
  messageTextContainer: {
    flex: 1,
    paddingHorizontal: 10,
  },
  messageName: {
    fontSize: 14,
    fontFamily: "Tajawal_700Bold",
    textAlign: "right",
    writingDirection: "rtl",
  },
  messageText: {
    fontSize: 12,
    color: "#777",
    fontFamily: "Tajawal_400Regular",
    textAlign: "right",
    marginTop: 3,
    writingDirection: "rtl",
  },
  messageMeta: {
    alignItems: "flex-end",
    minWidth: 60,
  },
  timeBadgeContainer: {
    alignItems: "flex-end",
  },
  messageTime: {
    fontSize: 12,
    color: "#aaa",
    fontFamily: "Tajawal_400Regular",
    textAlign: "right",
  },
  badge: {
    backgroundColor: Colors.primary,
    borderRadius: 5,
    paddingHorizontal: 6,
    marginTop: 4,
    height: 20,
    minWidth: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  badgeText: {
    color: Colors.primary50,
    fontSize: 12,
    fontFamily: "Tajawal_700Bold",
  },
  navigationLabel: {
    fontFamily: "Tajawal_500Medium",
    fontSize: 12,
    marginTop: 2,
  },
});
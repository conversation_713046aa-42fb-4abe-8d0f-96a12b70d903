import { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  Image,
  FlatList,
  TouchableOpacity,
  ScrollView,
  I18nManager,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import Colors from "../components/Colors";
import { ListUsers, ListUserProfile } from "../components/UserLists";

// Force RTL for entire app
I18nManager.forceRTL(true);
I18nManager.allowRTL(true);

const HomeScreen = () => {
  const [activeTab, setActiveTab] = useState("direct");

  const renderTopProfiles = () => {
    const data = ListUserProfile();
    return (
      <ScrollView
        horizontal={true}
        showsHorizontalScrollIndicator={false}
        style={styles.topProfileContainer}
        contentContainerStyle={styles.topProfileContent}
        initialScrollIndex={data.length} // Starts from the end (right)
        getItemLayout={(_, index) => ({
          length: 81, // width of item + margin (65 + 8 + 8)
          offset: 81 * index,
          index,
        })}
      >
        {data.map((user, index) => (
          <View key={index} style={styles.profileItem}>
            <Image source={user.avatar} style={styles.profileImage} />
            <Text style={styles.profileName}>{user.name}</Text>
          </View>
        ))}
        <TouchableOpacity style={styles.addProfile}>
          <Ionicons name="add" size={24} color="#fff" />
        </TouchableOpacity>
      </ScrollView>
    );
  };

  const renderMessageItem = ({ item }) => (
    <View style={styles.messageItem}>
      <View style={styles.messageMeta}>
        <View style={styles.timeBadgeContainer}>
          <Text style={styles.messageTime}>{item.time}</Text>
          {item.unread > 0 && (
            <View style={styles.badge}>
              <Text style={styles.badgeText}>{item.unread}</Text>
            </View>
          )}
        </View>
      </View>
      <View style={styles.messageTextContainer}>
        <Text style={styles.messageName}>{item.name}</Text>
        <Text style={styles.messageText}>{item.lastMessage}</Text>
      </View>
      <View style={styles.avatarCircle}>
        <Image source={item.avatar} style={styles.avatarImage} />
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerText}>الفريق الرئيسي</Text>
        <MaterialCommunityIcons name="inbox-multiple-outline" size={24} />
      </View>

      {/* Top Profile Stories */}
      {renderTopProfiles()}

      {/* Tabs */}
      <View style={styles.tabs}>
        <TouchableOpacity
          onPress={() => setActiveTab("archived")}
          style={styles.tabButton}
        >
          <Text
            style={[
              styles.tabText,
              activeTab === "archived" && styles.activeTab,
            ]}
          >
            المفضلة
          </Text>
        </TouchableOpacity>
        <TouchableOpacity 
          onPress={() => setActiveTab("channels")}
          style={styles.tabButton}
        >
          <Text
            style={[
              styles.tabText,
              activeTab === "channels" && styles.activeTab,
            ]}
          >
            القنوات
          </Text>
        </TouchableOpacity>
        <TouchableOpacity 
          onPress={() => setActiveTab("direct")}
          style={styles.tabButton}
        >
          <Text
            style={[
              styles.tabText,
              activeTab === "direct" && styles.activeTab,
            ]}
          >
            الرسائل المباشرة
          </Text>
        </TouchableOpacity>
      </View>

      <FlatList
        data={ListUsers()}
        keyExtractor={(item) => item.id.toString()}
        renderItem={renderMessageItem}
        contentContainerStyle={styles.messagesListContent}
      />
    </View>
  );
};

export default HomeScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 50,
    backgroundColor: "#fff",
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    paddingHorizontal: 10,
    marginBottom: 10,
    paddingRight: 20,
  },
  headerText: {
    fontSize: 18,
    fontFamily: "Tajawal_700Bold",
    textAlign: 'left',
    alignItems: 'flex-start',
  },
  topProfileContainer: {
    marginVertical: 5,
    paddingHorizontal: 10,
    flexDirection: 'row',
  },
  topProfileContent: {
    flexDirection: 'row',
    paddingHorizontal: 5,
  },
  profileItem: {
    alignItems: 'center',
    marginHorizontal: 8,
    marginBottom: 30,
    width: 65, // Fixed width for accurate scroll position
  },
  profileImage: {
    width: 65,
    height: 65,
    borderRadius: 30,
    marginBottom: 4,
  },
  profileName: {
    fontSize: 12,
    fontFamily: "Tajawal_500Medium",
    textAlign: 'center',
  },
  addProfile: {
    width: 65,
    height: 65,
    borderRadius: 30,
    backgroundColor: "#0B735F",
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 8,
  },
  tabs: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    borderBottomWidth: 1,
    borderColor: "#eee",
    paddingVertical: 10,
  },
  tabButton: {
    paddingHorizontal: 10,
  },
  tabText: {
    fontSize: 14,
    color: "#777",
    fontFamily: "Tajawal_500Medium",
    textAlign: 'center',
  },
  activeTab: {
    color: "#0B735F",
    borderBottomWidth: 2,
    borderBottomColor: "#0B735F",
    paddingBottom: 5,
    fontFamily: "Tajawal_700Bold",
  },
  messagesListContent: {
    paddingBottom: 1,
  },
  messageItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 10,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderColor: "#f0f0f0",
  },
  avatarCircle: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 10,
  },
  avatarImage: {
    width: 44,
    height: 44,
    borderRadius: 22,
  },
  messageTextContainer: {
    flex: 1,
    paddingHorizontal: 10,
  },
  messageName: {
    fontSize: 14,
    fontFamily: "Tajawal_700Bold",
    textAlign: 'right',
  },
  messageText: {
    fontSize: 12,
    color: "#777",
    fontFamily: "Tajawal_400Regular",
    textAlign: 'right',
    marginTop: 3,
  },
  messageMeta: {
    alignItems: 'center',
    minWidth: 60,
    marginLeft: -10,
  },
  timeBadgeContainer: {
    alignItems: 'center',
  },
  messageTime: {
    fontSize: 12,
    color: "#aaa",
    fontFamily: "Tajawal_400Regular",
  },
  badge: {
    backgroundColor: Colors.primary,
    borderRadius: 5,
    paddingHorizontal: 6,
    marginTop: 4,
    height: 20,
    width: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  badgeText: {
    color: Colors.primary50,
    fontSize: 12,
    fontFamily: "Tajawal_700Bold",
  },
});
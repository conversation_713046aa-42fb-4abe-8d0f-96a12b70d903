import { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  Image,
  FlatList,
  TouchableOpacity,
  ScrollView,
  I18nManager,
} from "react-native";
import { Ionicons } from "@expo/vector-icons/Ionicons";
import Colors from "../components/Colors";
import { ListUsers, ListUserProfile } from "../components/UserLists";

// Force RTL for entire app (only needs to run once)
I18nManager.forceRTL(true);

const HomeScreen = () => {
  const [activeTab, setActiveTab] = useState("direct");

  const renderTopProfiles = () => (
    <ScrollView
      horizontal={true}
      showsHorizontalScrollIndicator={false}
      style={styles.topProfileContainer}
      contentContainerStyle={styles.topProfileContent}
    >
      <TouchableOpacity style={styles.addProfile}>
        <Ionicons name="add" size={24} color="#fff" />
      </TouchableOpacity>
      {ListUserProfile().map((user, index) => (
        <View key={index} style={styles.profileItem}>
          <Image source={user.avatar} style={styles.profileImage} />
          <Text style={styles.profileName}>{user.name}</Text>
        </View>
      ))}
    </ScrollView>
  );

  const renderMessageItem = ({ item }) => (
    <View style={styles.messageItem}>
      <View style={styles.avatarCircle}>
        <Image source={item.avatar} style={styles.avatarImage} />
      </View>
      <View style={styles.messageTextContainer}>
        <Text style={styles.messageName}>{item.name}</Text>
        <Text style={styles.messageText}>السلام عليكم</Text>
      </View>
      <View style={styles.messageMeta}>
        <Text style={styles.messageTime}>منذ ١٢ دقيقة</Text>
        <View style={styles.badge}>
          <Text style={styles.badgeText}>٢</Text>
        </View>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Ionicons name="mail-outline" size={24} />
        <Text style={styles.headerText}>الفريق الرئيسي</Text>
      </View>

      {/* Top Profile Stories */}
      {renderTopProfiles()}

      {/* Tabs */}
      <View style={styles.tabs}>
        <TouchableOpacity onPress={() => setActiveTab("direct")}>
          <Text
            style={[styles.tabText, activeTab === "direct" && styles.activeTab]}
          >
            الرسائل المباشرة
          </Text>
        </TouchableOpacity>
        <TouchableOpacity onPress={() => setActiveTab("channels")}>
          <Text
            style={[styles.tabText, activeTab === "channels" && styles.activeTab]}
          >
            القنوات
          </Text>
        </TouchableOpacity>
        <TouchableOpacity onPress={() => setActiveTab("archived")}>
          <Text
            style={[styles.tabText, activeTab === "archived" && styles.activeTab]}
          >
            المؤرشفة
          </Text>
        </TouchableOpacity>
      </View>

      {/* Message List */}
      <FlatList
        data={ListUsers()}
        keyExtractor={(item) => item.id.toString()}
        renderItem={renderMessageItem}
        contentContainerStyle={{ paddingBottom: 1 }}
        style={styles.messagesList}
      />
    </View>
  );
};

export default HomeScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 50,
    backgroundColor: "#fff",
    direction: "rtl",
  },
  header: {
    flexDirection: "row-reverse",
    alignItems: "center",
    paddingHorizontal: 20,
    marginBottom: 10,
  },
  headerText: {
    fontSize: 18,
    marginLeft: 10,
    fontFamily: "Tajawal_700Bold",
    writingDirection: "rtl",
    textAlign: "right",
  },
  topProfileContainer: {
    marginVertical: 20,
    paddingHorizontal: 10,
  },
  topProfileContent: {
    flexDirection: "row-reverse",
    paddingHorizontal: 5,
  },
  profileItem: {
    alignItems: "center",
    marginHorizontal: 8,
    marginBottom: 30,
  },
  profileImage: {
    width: 65,
    height: 65,
    borderRadius: 30,
    marginBottom: 4,
  },
  profileName: {
    fontSize: 12,
    fontFamily: "Tajawal_500Medium",
    writingDirection: "rtl",
    textAlign: "center",
  },
  addProfile: {
    width: 65,
    height: 65,
    borderRadius: 30,
    backgroundColor: "#0B735F",
    justifyContent: "center",
    alignItems: "center",
    marginHorizontal: 8,
  },
  tabs: {
    flexDirection: "row-reverse",
    justifyContent: "space-around",
    borderBottomWidth: 1,
    borderColor: "#eee",
    paddingVertical: 10,
  },
  tabText: {
    fontSize: 14,
    color: "#777",
    fontFamily: "Tajawal_500Medium",
    writingDirection: "rtl",
    textAlign: "center",
  },
  activeTab: {
    color: "#0B735F",
    borderBottomWidth: 2,
    borderBottomColor: "#0B735F",
    paddingBottom: 5,
    fontFamily: "Tajawal_700Bold",
  },
  messagesList: {
    marginTop: 1,
  },
  messageItem: {
    flexDirection: "row-reverse",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderColor: "#f0f0f0",
  },
  avatarCircle: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: "center",
    alignItems: "center",
    marginLeft: 10,
  },
  avatarImage: {
    width: 44,
    height: 44,
    borderRadius: 22,
  },
  messageTextContainer: {
    flex: 1,
    paddingHorizontal: 10,
  },
  messageName: {
    fontSize: 14,
    fontWeight: "bold",
    textAlign: "right",
    fontFamily: "Tajawal_700Bold",
    writingDirection: "rtl",
  },
  messageText: {
    fontSize: 12,
    color: "#777",
    textAlign: "right",
    fontFamily: "Tajawal_400Regular",
    writingDirection: "rtl",
  },
  messageMeta: {
    alignItems: "flex-start", // flipped to support RTL
  },
  messageTime: {
    fontSize: 12,
    color: "#aaa",
    fontFamily: "Tajawal_400Regular",
    textAlign: "left",
  },
  badge: {
    backgroundColor: Colors.primary,
    borderRadius: 10,
    paddingHorizontal: 6,
    marginTop: 4,
  },
  badgeText: {
    color: Colors.primary50,
    fontSize: 12,
    fontFamily: "Tajawal_700Bold",
    textAlign: "center",
  },
});

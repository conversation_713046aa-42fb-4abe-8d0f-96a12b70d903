import { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  ScrollView,
  I18nManager,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { ListUserProfile } from "../components/UserLists";

// Import separated tab components
import ArchivedTab from "../components/tabs/ArchivedTab";
import ChannelsTab from "../components/tabs/ChannelsTab";
import DirectMessagesTab from "../components/tabs/DirectMessagesTab";
import UserStoryAvatar from "../components/User-Story-Avatar/UserStoryAvatar";

// Force Arabic layout
I18nManager.forceRTL(true);
I18nManager.allowRTL(true);

const HomeScreen = () => {
  const [activeTab, setActiveTab] = useState("direct");

  const renderTopProfiles = () => {
    const data = ListUserProfile();
    return (
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.topProfileContainer}
        contentContainerStyle={styles.topProfileContent}
        directionalLockEnabled
      >
      {data.map((user) => (
        <UserStoryAvatar key={user.id} user={user} />
      ))}
      </ScrollView>
    );
  };

  const renderTabContent = () => {
    return (
      <View style={styles.tabContentContainer}>
        {activeTab === "direct" ? (
          <DirectMessagesTab />
        ) : activeTab === "archived" ? (
          <ArchivedTab />
        ) : (
          <ChannelsTab />
        )}
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <MaterialCommunityIcons name="inbox-multiple-outline" size={24} />
        <Text style={styles.headerText}>الفريق الرئيسي</Text>
        <View style={styles.headerButtons}>
          <TouchableOpacity
            style={[
              styles.iconButton,
              {
                backgroundColor: "#00987E1A",
                borderColor: "#00987E",
              },
              styles.headerIcon,
            ]}
          >
            <Image
              source={require("../assets/Sofa-Chat/sofa-chat-logo.png")}
              style={{ width: 16, height: 16 }}
            />
          </TouchableOpacity>
          <TouchableOpacity style={styles.iconButton}>
            <Ionicons name="search" size={18} color="#7F8887" />
          </TouchableOpacity>
          <TouchableOpacity style={styles.iconButton}>
            <Ionicons name="add" color="#7F8887" size={18} />
          </TouchableOpacity>
          <TouchableOpacity style={styles.iconButton}>
            <Ionicons name="ellipsis-vertical" color="#7F8887" size={18} />
          </TouchableOpacity>
        </View>
      </View>

      {renderTopProfiles()}

      <View style={styles.tabs}>
        <TouchableOpacity
          onPress={() => setActiveTab("archived")}
          style={styles.tabButton}
        >
          <Text
            style={[
              styles.tabText,
              activeTab === "archived" && styles.activeTab,
            ]}
          >
            المفضلة
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={() => setActiveTab("channels")}
          style={styles.tabButton}
        >
          <Text
            style={[
              styles.tabText,
              activeTab === "channels" && styles.activeTab,
            ]}
          >
            القنوات
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={() => setActiveTab("direct")}
          style={styles.tabButton}
        >
          <Text
            style={[
              styles.tabText,
              activeTab === "direct" && styles.activeTab,
            ]}
          >
            الرسائل المباشرة
          </Text>
        </TouchableOpacity>
      </View>

      {renderTabContent()}
    </View>
  );
};

export default HomeScreen;

const styles = StyleSheet.create({
  container: {
    backgroundColor: "#fff",
    flex: 1,
    paddingTop: 50,
  },
  header: {
    flexDirection: "row-reverse",
    alignItems: "center",
    paddingHorizontal: 10,
    marginBottom: 10,
  },
  headerText: {
    fontSize: 18,
    fontFamily: "IBMPlexSansArabic_700Bold",
    textAlign: "right",
    writingDirection: "rtl",
    marginHorizontal: 10,
  },
  headerButtons: {
    flexDirection: "row-reverse",
    alignItems: "center",
    gap: 10,
  },
  headerIcon: {
    marginRight: 35,
  },
  iconButton: {
    width: 32,
    height: 32,
    borderRadius: 8,
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 1,
    padding: 5,
    gap: 8,
    borderColor: "#0012101A",
  },
  topProfileContainer: {
    marginVertical: 5,
    paddingHorizontal: 10,
  },
  topProfileContent: {
    flexDirection: "row-reverse",
    paddingHorizontal: 10,
  },
  profileItem: {
    alignItems: "center",
    marginHorizontal: 10,
    marginBottom: 20,
    width: 54,
  },
  profileImageWrapper: {
    position: "relative",
  },
  profileImage: {
    width: 54,
    height: 54,
    borderRadius: 27,
    marginBottom: 5,
  },
  profileName: {
    fontSize: 12,
    fontFamily: "IBMPlexSansArabic_500Medium",
    textAlign: "center",
    writingDirection: "rtl",
  },
  addBadge: {
    position: "absolute",
    bottom: 0,
    right: 0,
    width: 17.78,
    height: 17.78,
    borderRadius: 10,
    backgroundColor: "#00987E",
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#FFFFFF",
  },
  tabs: {
    flexDirection: "row",
    justifyContent: "space-around",
    borderBottomWidth: 1,
    borderColor: "#eee",
    paddingVertical: 10,
  },
  tabButton: {
    paddingHorizontal: 10,
  },
  tabText: {
    fontSize: 14,
    color: "#777",
    fontFamily: "IBMPlexSansArabic_500Medium",
    textAlign: "center",
    writingDirection: "rtl",
  },
  activeTab: {
    color: "#0B735F",
    borderBottomWidth: 2,
    borderBottomColor: "#0B735F",
    paddingBottom: 5,
    fontFamily: "IBMPlexSansArabic_700Bold",
  },
  tabContentContainer: {
    height: 430,
    backgroundColor: "#fff",
  },
});

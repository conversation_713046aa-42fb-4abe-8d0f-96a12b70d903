{"version": 3, "names": ["Platform", "setColor", "grey400", "grey800", "grey50", "grey700", "white", "black", "getCheckedColor", "theme", "color", "isV3", "colors", "primary", "accent", "getThumbTintColor", "disabled", "value", "checkedColor", "isIOS", "OS", "undefined", "dark", "getOnTintColor", "alpha", "rgb", "string", "getSwitchColor", "onTintColor", "thumbTintColor"], "sourceRoot": "../../../../src", "sources": ["components/Switch/utils.ts"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,cAAc;AAEvC,OAAOC,QAAQ,MAAM,OAAO;AAE5B,SACEC,OAAO,EACPC,OAAO,EACPC,MAAM,EACNC,OAAO,EACPC,KAAK,EACLC,KAAK,QACA,+BAA+B;AAStC,MAAMC,eAAe,GAAGA,CAAC;EACvBC,KAAK;EACLC;AAIF,CAAC,KAAK;EACJ,IAAIA,KAAK,EAAE;IACT,OAAOA,KAAK;EACd;EAEA,IAAID,KAAK,CAACE,IAAI,EAAE;IACd,OAAOF,KAAK,CAACG,MAAM,CAACC,OAAO;EAC7B;EAEA,OAAOJ,KAAK,CAACG,MAAM,CAACE,MAAM;AAC5B,CAAC;AAED,MAAMC,iBAAiB,GAAGA,CAAC;EACzBN,KAAK;EACLO,QAAQ;EACRC,KAAK;EACLC;AACoC,CAAC,KAAK;EAC1C,MAAMC,KAAK,GAAGnB,QAAQ,CAACoB,EAAE,KAAK,KAAK;EAEnC,IAAID,KAAK,EAAE;IACT,OAAOE,SAAS;EAClB;EAEA,IAAIL,QAAQ,EAAE;IACZ,IAAIP,KAAK,CAACa,IAAI,EAAE;MACd,OAAOnB,OAAO;IAChB;IACA,OAAOD,OAAO;EAChB;EAEA,IAAIe,KAAK,EAAE;IACT,OAAOC,YAAY;EACrB;EAEA,IAAIT,KAAK,CAACa,IAAI,EAAE;IACd,OAAOpB,OAAO;EAChB;EACA,OAAOE,MAAM;AACf,CAAC;AAED,MAAMmB,cAAc,GAAGA,CAAC;EACtBd,KAAK;EACLO,QAAQ;EACRC,KAAK;EACLC;AACoC,CAAC,KAAK;EAC1C,MAAMC,KAAK,GAAGnB,QAAQ,CAACoB,EAAE,KAAK,KAAK;EAEnC,IAAID,KAAK,EAAE;IACT,OAAOD,YAAY;EACrB;EAEA,IAAIF,QAAQ,EAAE;IACZ,IAAIP,KAAK,CAACa,IAAI,EAAE;MACd,IAAIb,KAAK,CAACE,IAAI,EAAE;QACd,OAAOV,QAAQ,CAACK,KAAK,CAAC,CAACkB,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;MACnD;MACA,OAAOzB,QAAQ,CAACK,KAAK,CAAC,CAACkB,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;IAClD;IACA,OAAOzB,QAAQ,CAACM,KAAK,CAAC,CAACiB,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EACnD;EAEA,IAAIT,KAAK,EAAE;IACT,OAAOhB,QAAQ,CAACiB,YAAY,CAAC,CAACM,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EACzD;EAEA,IAAIjB,KAAK,CAACa,IAAI,EAAE;IACd,OAAOjB,OAAO;EAChB;EACA,OAAO,oBAAoB;AAC7B,CAAC;AAED,OAAO,MAAMsB,cAAc,GAAGA,CAAC;EAC7BlB,KAAK;EACLO,QAAQ;EACRC,KAAK;EACLP;AAC8B,CAAC,KAAK;EACpC,MAAMQ,YAAY,GAAGV,eAAe,CAAC;IAAEC,KAAK;IAAEC;EAAM,CAAC,CAAC;EAEtD,OAAO;IACLkB,WAAW,EAAEL,cAAc,CAAC;MAAEd,KAAK;MAAEO,QAAQ;MAAEC,KAAK;MAAEC;IAAa,CAAC,CAAC;IACrEW,cAAc,EAAEd,iBAAiB,CAAC;MAAEN,KAAK;MAAEO,QAAQ;MAAEC,KAAK;MAAEC;IAAa,CAAC,CAAC;IAC3EA;EACF,CAAC;AACH,CAAC", "ignoreList": []}
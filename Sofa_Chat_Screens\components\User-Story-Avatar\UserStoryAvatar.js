import { View, Image, Text, TouchableOpacity, StyleSheet } from "react-native";
import { Svg, Path } from "react-native-svg";
import { Ionicons } from "@expo/vector-icons";

const UserStoryAvatar = ({ user }) => {
  const storyCount = user.stories || 0;
  const imageRadius = 27; // Profile image radius
  const strokeWidth = 3; // Thicker stroke for better visibility
  const segmentRadius = imageRadius + strokeWidth + 2; // Radius for segments (slightly outside image)
  const center = segmentRadius + strokeWidth; // Center point of SVG
  const svgSize = (segmentRadius + strokeWidth) * 2; // Total SVG size
  const segments = [];

  if (storyCount === 0) {
    // Full gray circle when no stories
    segments.push(
      <Path
        key={"no-story"}
        d={`M ${center} ${strokeWidth} A ${segmentRadius} ${segmentRadius} 0 1 1 ${
          center - 0.1
        } ${strokeWidth}`}
        stroke="#E9E9E9"
        strokeWidth={strokeWidth}
        fill="none"
      />
    );
  } else {
    // Add small gaps between segments for better visual separation
    const gapAngle = (Math.PI / 180) * 2; // 2 degree gap between segments
    const segmentAngle = (2 * Math.PI - gapAngle * storyCount) / storyCount;

    for (let i = 0; i < storyCount; i++) {
      // Start from top (-90 degrees) and rotate clockwise
      const startAngle = -Math.PI / 2 + i * (segmentAngle + gapAngle);
      const endAngle = startAngle + segmentAngle;

      const x1 = center + segmentRadius * Math.cos(startAngle);
      const y1 = center + segmentRadius * Math.sin(startAngle);
      const x2 = center + segmentRadius * Math.cos(endAngle);
      const y2 = center + segmentRadius * Math.sin(endAngle);

      const largeArcFlag = segmentAngle > Math.PI ? 1 : 0;

      segments.push(
        <Path
          key={i}
          d={`M ${x1} ${y1} A ${segmentRadius} ${segmentRadius} 0 ${largeArcFlag} 1 ${x2} ${y2}`}
          stroke="#00987E"
          strokeWidth={strokeWidth}
          fill="none"
          strokeLinecap="round" // Rounded ends for better appearance
        />
      );
    }
  }

  return (
    <View style={styles.profileItem}>
      <View style={styles.profileImageWrapper}>
        <Svg height={svgSize} width={svgSize} style={styles.svgContainer}>
          {segments}
        </Svg>
        <Image source={user.avatar} style={styles.profileImage} />
        {user.id === 9 && (
          <TouchableOpacity style={styles.addBadge}>
            <Ionicons name="add" size={10} color="#FFFFFF" />
          </TouchableOpacity>
        )}
      </View>
      <Text style={styles.profileName}>{user.name}</Text>
    </View>
  );
};

export default UserStoryAvatar;

const styles = StyleSheet.create({
  profileItem: {
    alignItems: "center",
    marginHorizontal: 10,
    marginBottom: 20,
    width: 70, // Increased to accommodate segments
  },
  profileImageWrapper: {
    position: "relative",
    width: 70, // Increased to accommodate segments
    height: 70, // Increased to accommodate segments
    alignItems: "center",
    justifyContent: "center",
  },
  svgContainer: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  profileImage: {
    width: 54,
    height: 54,
    borderRadius: 27,
    marginBottom: 5,
    zIndex: 1, // Ensure image is above SVG
  },
  profileName: {
    fontSize: 12,
    fontFamily: "IBMPlexSansArabic_500Medium",
    textAlign: "center",
    writingDirection: "rtl",
    marginTop: 5,
  },
  addBadge: {
    position: "absolute",
    bottom: 5,
    right: 5,
    width: 18,
    height: 18,
    borderRadius: 9,
    backgroundColor: "#00987E",
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 2,
    borderColor: "#FFFFFF",
    zIndex: 2, // Ensure badge is above everything
  },
});

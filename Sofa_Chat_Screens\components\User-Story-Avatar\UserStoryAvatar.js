import { View, Image, Text, TouchableOpacity, StyleSheet } from "react-native";
import { Svg, Path } from "react-native-svg";
import { Ionicons } from "@expo/vector-icons";

const UserStoryAvatar = ({ user }) => {
  const storyCount = user.stories || 0;
  const radius = 27;
  const strokeWidth = 2;
  const segments = [];

  if (storyCount === 0) {
    // Full gray circle
    segments.push(
      <Path
        key={"no-story"}
        d={`M ${radius} 0 A ${radius} ${radius} 0 1 1 ${radius - 0.1} 0`}
        stroke="#E9E9E9"
        strokeWidth={strokeWidth}
        fill="none"
      />
    );
  } else {
    for (let i = 0; i < storyCount; i++) {
      const startAngle = (i / storyCount) * 2 * Math.PI;
      const endAngle = ((i + 1) / storyCount) * 2 * Math.PI;

      const x1 = radius + radius * Math.cos(startAngle);
      const y1 = radius + radius * Math.sin(startAngle);
      const x2 = radius + radius * Math.cos(endAngle);
      const y2 = radius + radius * Math.sin(endAngle);

      const largeArcFlag = endAngle - startAngle > Math.PI ? 1 : 0;

      segments.push(
        <Path
          key={i}
          d={`M ${x1} ${y1} A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2}`}
          stroke="#00987E"
          strokeWidth={strokeWidth}
          fill="none"
        />
      );
    }
  }

  return (
    <View style={styles.profileItem}>
      <View style={styles.profileImageWrapper}>
        <Svg
          height={2 * radius + strokeWidth}
          width={2 * radius + strokeWidth}
          style={StyleSheet.absoluteFill}
        >
          {segments}
        </Svg>
        <Image source={user.avatar} style={styles.profileImage} />
        {user.id === 9 && (
          <TouchableOpacity style={styles.addBadge}>
            <Ionicons name="add" size={10} color="#FFFFFF" />
          </TouchableOpacity>
        )}
      </View>
      <Text style={styles.profileName}>{user.name}</Text>
    </View>
  );
};

export default UserStoryAvatar;

const styles = StyleSheet.create({
  profileItem: {
    alignItems: "center",
    marginHorizontal: 10,
    marginBottom: 20,
    width: 54,
  },
  profileImageWrapper: {
    position: "relative",
    width: 56,
    height: 56,
    alignItems: "center",
    justifyContent: "center",
  },
  profileImage: {
    width: 54,
    height: 54,
    borderRadius: 27,
    marginBottom: 5,
  },
  profileName: {
    fontSize: 12,
    fontFamily: "IBMPlexSansArabic_500Medium",
    textAlign: "center",
    writingDirection: "rtl",
  },
  addBadge: {
    position: "absolute",
    bottom: 0,
    right: 0,
    width: 17.78,
    height: 17.78,
    borderRadius: 10,
    backgroundColor: "#00987E",
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#FFFFFF",
  },
});

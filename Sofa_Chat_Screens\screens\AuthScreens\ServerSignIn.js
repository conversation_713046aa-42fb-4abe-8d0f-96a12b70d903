import {
  View,
  Text,
  TextInput,
  Pressable,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  Dimensions,
  I18nManager,
} from "react-native";
import { useState, useRef } from "react";
import { LinearGradient } from "expo-linear-gradient";
import { CommonHeader } from "../../components/CommonHeader";
import Colors from "../../components/Colors";
import { useNavigation } from "@react-navigation/native";

const { height, width } = Dimensions.get("window");

// Force RTL layout for Arabic
I18nManager.forceRTL(true);
I18nManager.allowRTL(true);

const ServerSignIn = () => {
  const navigation = useNavigation(); // Get the navigator from the context
  const [serverUrl, setServerUrl] = useState("");
  const [serverName, setServerName] = useState("");
  const scrollViewRef = useRef(null);

  const handleConnect = () => {
    console.log("Connecting to:", serverUrl, "Name:", serverName);
    navigation.replace("LogIn");
  };

  const scrollToInput = (inputPosition) => {
    if (scrollViewRef.current) {
      const maxScrollY = Math.max(0, inputPosition - height * 0.3);
      const minScrollY = 0;
      const targetScrollY = Math.min(
        maxScrollY,
        Math.max(minScrollY, inputPosition - height * 0.4)
      );

      scrollViewRef.current.scrollTo({
        y: targetScrollY,
        animated: true,
      });
    }
  };

  return (
    <LinearGradient
      colors={["#E5F9F6", "#FFFFFF", "#E5F9F6", "#E5F9F6"]}
      style={styles.LinearGradientContainer}
      start={{ x: 0.5, y: 0 }}
      end={{ x: 0.5, y: 1 }}
    >
      <CommonHeader />
      <SafeAreaView style={styles.container}>
        <ScrollView
          ref={scrollViewRef}
          contentContainerStyle={styles.scrollViewContent}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
          bounces={false}
          scrollEventThrottle={16}
          onScroll={(event) => {
            const scrollY = event.nativeEvent.contentOffset.y;
            const maxScrollY = height * 0.25;
            if (scrollY > maxScrollY) {
              scrollViewRef.current?.scrollTo({
                y: maxScrollY,
                animated: false,
              });
            }
          }}
        >
          <View style={styles.content}>
            <Text style={styles.subtitle}>الاتصال بالخادم</Text>
            <Text style={[styles.instruction, { marginBottom: 40 }]}>
              قم بإعداد الخادم الأول الخاص بك
            </Text>

            <View style={styles.formGroup}>
              <Text style={styles.label}>عنوان الـ URL للخادم</Text>
              <TextInput
                key="server-url-input-stable"
                style={styles.input}
                placeholder="URL"
                placeholderTextColor="#999"
                value={serverUrl}
                onChangeText={setServerUrl}
                textAlign="right"
                textAlignVertical="center"
                keyboardType="url"
                autoCapitalize="none"
                autoCorrect={false}
                returnKeyType="next"
                enablesReturnKeyAutomatically={false}
                onFocus={() => scrollToInput(100)}
              />
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>اسم عرض الخادم</Text>
              <TextInput
                key="server-name-input-stable"
                style={styles.input}
                placeholder="الاسم الذي سيتم عرضه في قائمة الخوادم"
                placeholderTextColor="#999"
                value={serverName}
                onChangeText={setServerName}
                textAlign="right"
                textAlignVertical="center"
                keyboardType="default"
                autoCapitalize="none"
                autoCorrect={false}
                returnKeyType="done"
                enablesReturnKeyAutomatically={false}
                onFocus={() => scrollToInput(200)}
              />
            </View>

            <Pressable
              style={styles.connectButton}
              onPress={handleConnect}
              android_ripple={{
                color: Colors.primary50,
                overflow: "hidden",
              }}
            >
              <Text style={styles.connectButtonText}>اتصال</Text>
            </Pressable>
          </View>
        </ScrollView>
      </SafeAreaView>
    </LinearGradient>
  );
};

export default ServerSignIn;

const styles = StyleSheet.create({
  LinearGradientContainer: {
    flex: 1,
    alignItems: "center",
    paddingTop: height * 0.08,
    paddingBottom: height * 0.14,
  },
  container: {
    flex: 1,
    width: "100%",
  },
  scrollViewContent: {
    flexGrow: 1,
    paddingBottom: 100,
    minHeight: height,
  },
  content: {
    width: width * 0.9,
    alignSelf: "center",
    padding: 16,
    borderRadius: 16,
    marginTop: 40,
    marginBottom: 50,
    paddingTop: 20,
  },
  subtitle: {
    fontSize: 32,
    textAlign: "right",
    marginBottom: 8,
    marginTop: 10,
    color: "#2C3E50",
    writingDirection: "rtl",
    fontFamily: "IBMPlexSansArabic_700Bold",
  },
  instruction: {
    fontSize: 12,
    color: "#7F8C8D",
    textAlign: "right",
    marginBottom: 18,
    writingDirection: "rtl",
    lineHeight: 16,
    fontFamily: "IBMPlexSansArabic_400Regular",
  },
  formGroup: {
    marginBottom: 24,
  },
  label: {
    fontSize: 14,
    marginBottom: 10,
    color: "#34495E",
    textAlign: "right",
    writingDirection: "rtl",
    fontFamily: "IBMPlexSansArabic_700Bold",
  },
  input: {
    borderWidth: 1,
    borderColor: "rgb(245, 245, 245)",
    borderRadius: 10,
    padding: 8,
    fontSize: 12,
    height: 50,
    width: "110%",
    marginLeft: -10,
    backgroundColor: "#FFFFFF",
    alignContent: "center",
    textAlign: "right",
    writingDirection: "rtl",
    fontFamily: "IBMPlexSansArabic_400Regular",
  },
  connectButton: {
    backgroundColor: Colors.primary,
    borderRadius: 10,
    padding: 4,
    alignItems: "center",
    marginTop: 20,
    height: 40,
    justifyContent: "center",
  },
  connectButtonText: {
    color: Colors.primary50,
    fontSize: 18,
    fontFamily: "IBMPlexSansArabic_500Medium",
  },
});

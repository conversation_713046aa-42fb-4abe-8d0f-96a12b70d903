import {
  View,
  Text,
  Image,
  StyleSheet,
  Dimensions,
  I18nManager,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { useNavigation } from "@react-navigation/native";
import { useEffect } from "react";

import { CommonHeader } from "../components/CommonHeader";

// Force RTL layout for Arabic
I18nManager.forceRTL(true);
I18nManager.allowRTL(true);

const { width, height } = Dimensions.get("window");

const SplashScreen = () => {
  const navigation = useNavigation();

  useEffect(() => {
    const timer = setTimeout(() => {
      navigation.replace("ServerSignIn");
    }, 3000);

    return () => clearTimeout(timer);
  }, []);

  return (
    <LinearGradient
      colors={["#E5F9F6", "#FFFFFF", "#E5F9F6", "#E5F9F6"]}
      style={styles.container}
      start={{ x: 0.5, y: 0 }}
      end={{ x: 0.5, y: 1 }}
    >
      <CommonHeader />
      <View style={styles.illustrationContainer}>
        <Image
          source={require("../assets/Sofa-Chat/sofa-chat-splash-image.png")}
          style={styles.illustration}
          resizeMode="contain"
        />
      </View>

      <View style={styles.textContainer}>
        <Text style={styles.welcomeText}>مرحبًا بكم في Meta Chat!</Text>
        <Text style={styles.subText}>
          خصوصيتكم وأمان محادثاتكم هي أولويتنا.
        </Text>
      </View>
    </LinearGradient>
  );
};

export default SplashScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: "center",
    paddingTop: height * 0.08,
    paddingBottom: height * 0.12,
  },
  // Illustration
  illustrationContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    width: "100%",
  },
  illustration: {
    width: width * 0.9,
    height: width * 0.9,
    maxWidth: 390,
    maxHeight: 390,
  },
  // Text
  textContainer: {
    alignItems: "center",
    width: "100%",
    paddingHorizontal: 40,
    marginBottom: 90,
  },
  welcomeText: {
    fontSize: 26,
    color: "#111",
    marginBottom: 12,
    textAlign: "center",
    fontFamily: "IBMPlexSansArabic_700Bold",
    writingDirection: "rtl",
  },
  subText: {
    fontSize: 18,
    color: "#555",
    textAlign: "center",
    lineHeight: 28,
    fontFamily: "IBMPlexSansArabic_400Regular",
    writingDirection: "rtl",
  },
});

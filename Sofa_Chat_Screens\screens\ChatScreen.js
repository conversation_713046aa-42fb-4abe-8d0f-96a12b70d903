import React from "react";
import { View, Text, StyleSheet, I18nManager } from "react-native";

// Force RTL layout for Arabic
I18nManager.forceRTL(true);
I18nManager.allowRTL(true);

const ChatScreen = () => {
  return (
    <View style={styles.container}>
      <Text style={styles.text}>شاشة البحث</Text>
      <Text style={styles.subText}>قريباً...</Text>
    </View>
  );
};

export default ChatScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#fff",
  },
  text: {
    fontSize: 24,
    fontFamily: "IBMPlexSansArabic_700Bold",
    textAlign: "center",
    writingDirection: "rtl",
    color: "#2C3E50",
    marginBottom: 10,
  },
  subText: {
    fontSize: 16,
    fontFamily: "IBMPlexSansArabic_400Regular",
    textAlign: "center",
    writingDirection: "rtl",
    color: "#7F8C8D",
  },
});

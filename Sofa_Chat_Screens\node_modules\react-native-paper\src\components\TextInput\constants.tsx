export const MAXIMIZED_LABEL_FONT_SIZE = 16;
export const MINIMIZED_LABEL_FONT_SIZE = 12;
export const LABEL_WIGGLE_X_OFFSET = 4;

export const ADORNMENT_SIZE = 24;
export const MIN_WIDTH = 100;

//Text input affix offset
export const MD2_AFFIX_OFFSET = 12;
export const MD3_AFFIX_OFFSET = 16;

// Text input icon
export const ICON_SIZE = 24;
export const MD2_ICON_OFFSET = 12;
export const MD3_ICON_OFFSET = 16;

// Text input common
export const MD2_MIN_HEIGHT = 64;
export const MD3_MIN_HEIGHT = 56;
export const MD3_ADORNMENT_OFFSET = 16;
export const MD2_ADORNMENT_OFFSET = 12;
export const LABEL_PADDING_TOP_DENSE = 24;
export const LABEL_PADDING_TOP = 8;

// Text input flat
export const MD2_LABEL_PADDING_TOP = 30;
export const MD3_LABEL_PADDING_TOP = 26;

export const MD2_LABEL_PADDING_HORIZONTAL = 12;
export const MD3_LABEL_PADDING_HORIZONTAL = 16;

export const MD2_FLAT_INPUT_OFFSET = 8;
export const MD3_FLAT_INPUT_OFFSET = 16;

export const MINIMIZED_LABEL_Y_OFFSET = -18;
export const MIN_DENSE_HEIGHT_WL = 52;
export const MIN_DENSE_HEIGHT = 40;

// Text input outlined
export const MD2_INPUT_PADDING_HORIZONTAL = 14;
export const MD3_INPUT_PADDING_HORIZONTAL = 16;

// extra space to avoid overlapping input's text and icon
export const MD2_OUTLINED_INPUT_OFFSET = 8;
export const MD3_OUTLINED_INPUT_OFFSET = 16;

export const OUTLINE_MINIMIZED_LABEL_Y_OFFSET = -6;
export const MIN_DENSE_HEIGHT_OUTLINED = 48;

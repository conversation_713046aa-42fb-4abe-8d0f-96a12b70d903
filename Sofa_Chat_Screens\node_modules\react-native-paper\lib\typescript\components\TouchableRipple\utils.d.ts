import type { ColorValue } from 'react-native';
import type { InternalTheme } from '../../types';
export declare const getTouchableRippleColors: ({ theme, rippleColor, underlayColor, }: {
    theme: InternalTheme;
    rippleColor?: ColorValue | undefined;
    underlayColor?: string | undefined;
}) => {
    calculatedRippleColor: ColorValue;
    calculatedUnderlayColor: string;
};
//# sourceMappingURL=utils.d.ts.map
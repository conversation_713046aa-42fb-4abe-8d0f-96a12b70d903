// This file is generated by @expo-google-fonts/generator
// If you want to modify it, go to packages/generator/templates

export * from './useFonts';
export { default as __metadata__ } from './metadata.json';
export const IBMPlexSansArabic_100Thin = require('./100Thin/IBMPlexSansArabic_100Thin.ttf');
export const IBMPlexSansArabic_200ExtraLight = require('./200ExtraLight/IBMPlexSansArabic_200ExtraLight.ttf');
export const IBMPlexSansArabic_300Light = require('./300Light/IBMPlexSansArabic_300Light.ttf');
export const IBMPlexSansArabic_400Regular = require('./400Regular/IBMPlexSansArabic_400Regular.ttf');
export const IBMPlexSansArabic_500Medium = require('./500Medium/IBMPlexSansArabic_500Medium.ttf');
export const IBMPlexSansArabic_600SemiBold = require('./600SemiBold/IBMPlexSansArabic_600SemiBold.ttf');
export const IBMPlexSansArabic_700Bold = require('./700Bold/IBMPlexSansArabic_700Bold.ttf');

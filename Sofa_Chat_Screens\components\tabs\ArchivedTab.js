// components/tabs/ArchivedTab.js
import { ScrollView, Text, StyleSheet } from 'react-native';

const ArchivedTab = () => (
  <ScrollView contentContainerStyle={styles.container}>
    <Text style={styles.text}>لا توجد عناصر مفضلة حاليًا.</Text>
  </ScrollView>
);

export default ArchivedTab;

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 10,
    marginTop: 150,
  },
  text: {
    fontSize: 18,
    color: '#444',
    textAlign: 'center',
    writingDirection: 'rtl',
    fontFamily: 'IBMPlexSansArabic_500Medium',
  },
});

// components/tabs/DirectMessagesTab.js
import React from 'react';
import { FlatList, View, Text, Image, StyleSheet } from 'react-native';
import { ListUsers } from '../UserLists';

const DirectMessagesTab = () => {
  const renderMessageItem = ({ item }) => (
    <View style={styles.messageItem}>
      <View style={styles.messageMeta}>
        <View style={styles.timeBadgeContainer}>
          <Text style={styles.messageTime}>{item.time}</Text>
          {item.unread > 0 && (
            <View style={styles.badge}>
              <Text style={styles.badgeText}>{item.unread}</Text>
            </View>
          )}
        </View>
      </View>
      <View style={styles.messageTextContainer}>
        <Text style={styles.messageName}>{item.name}</Text>
        <Text style={styles.messageText}>{item.lastMessage}</Text>
      </View>
      <View style={styles.avatarCircle}>
        <Image source={item.avatar} style={styles.avatarImage} />
      </View>
    </View>
  );

  return (
    <FlatList
      data={ListUsers()}
      keyExtractor={(item) => item.id.toString()}
      renderItem={renderMessageItem}
      contentContainerStyle={{ paddingBottom: 1 }}
    />
  );
};

export default DirectMessagesTab;

const styles = StyleSheet.create({
  // Same styles from your original messageItem, etc.
  messageItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 10,
    marginLeft: -30,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderColor: '#f0f0f0',
  },
  avatarCircle: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 10,
  },
  avatarImage: {
    width: 44,
    height: 44,
    borderRadius: 22,
  },
  messageTextContainer: {
    flex: 1,
    paddingHorizontal: 10,
  },
  messageName: {
    fontSize: 14,
    fontFamily: 'IBMPlexSansArabic_700Bold',
    textAlign: 'right',
    writingDirection: 'rtl',
  },
  messageText: {
    fontSize: 12,
    color: '#777',
    fontFamily: 'IBMPlexSansArabic_400Regular',
    textAlign: 'right',
    marginTop: 3,
    writingDirection: 'rtl',
  },
  messageMeta: {
    alignItems: 'flex-end',
    minWidth: 60,
    marginRight: -10,
  },
  timeBadgeContainer: {
    alignItems: 'flex-end',
  },
  messageTime: {
    fontSize: 12,
    color: '#aaa',
    fontFamily: 'IBMPlexSansArabic_400Regular',
    textAlign: 'right',
  },
  badge: {
    backgroundColor: '#0B735F',
    borderRadius: 5,
    paddingHorizontal: 6,
    marginTop: 4,
    height: 20,
    width: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  badgeText: {
    color: '#fff',
    fontSize: 12,
    fontFamily: 'IBMPlexSansArabic_700Bold',
  },
});
